/*
 * Copyright (c) 2012-2014. Sencha Inc.
 */

package com.sencha.tools.compiler.builder.optimizer;

import com.sencha.logging.SenchaLogManager;
import com.sencha.tools.compiler.CompilerContext;
import com.sencha.tools.compiler.ast.AstUtil;
import com.sencha.tools.compiler.meta.Tag;
import com.sencha.tools.compiler.sources.*;
import com.sencha.util.JavaScriptHost;
import com.sencha.util.Locator;
import com.sencha.util.StringUtil;
import com.sencha.tools.compiler.ast.js.*;
import org.slf4j.Logger;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class XTemplatePrecompiler extends BaseFileOptimization {
    private static final Logger _logger = SenchaLogManager.getLogger();

    private static final JavaScriptHost _host = getJsHost();
    public static final String PrecompileTag = "cmd-xtpl-precompile";

    public static synchronized JavaScriptHost getJsHost() {
        JavaScriptHost host = new JavaScriptHost();
        host.load(new File(Locator.getBaseDir(), "js"), "xtemplate-all-optimizer.js");
        return host;
    }

    @Override
    public void optimize(CompilerContext context) {
        _logger.info("precompiling xtemplates...");
        super.optimize(context);
    }

    private BaseNodeVisitor<CompilerContext> _vis = new OptimizedNodeVisitor<CompilerContext>(){

        @Override
        public void onObjectProperty(ObjectProperty prop) {
            BaseNode right = prop.getRight();
            if(prop.getMember() != null) {
                if(right instanceof ArrayLiteral) {
                    ArrayLiteral target = (ArrayLiteral) right;
                    optimizeNode(prop, right, target.getElements());
                    return;
                } else if(right instanceof StringLiteral) {
                    List<BaseNode> elements = new ArrayList<BaseNode>();
                    elements.add(right);
                    optimizeNode(prop, right, elements);
                    return;
                }
            }
            super.onObjectProperty(prop);
        }

        @Override
        public void onNewExpression(NewExpression expr) {
            BaseNode target = expr.getTarget();
            String name = AstUtil.resolveName(target);
            if("Ext.XTemplate".equals(name)) {
                optimizeNode(expr, expr, expr.getArguments());
                return;
            }
            super.onNewExpression(expr);
        }

        private boolean optimizeNode(BaseNode src, BaseNode replace, List<BaseNode> elements) {
            boolean doOptimize = false;
            Tag tag = getTag(src);
            boolean safe = false;

            // Ext.XTemplate ctor call
            if(replace instanceof NewExpression) {
                doOptimize = true;
            }

            // might be a tpl property on a class body
            if(src.getMember() != null) {
                String propName = src.getMember().getName();
                if("tpl".equals(propName) || propName.endsWith("Tpl")) {
                    doOptimize = true;
                }
            }

            // we were explicitly told to optimize this node
            if(tag != null) {
                safe = "safe".equals(tag.getType());
                if("skip".equals(tag.getType())) {
                    doOptimize = false;
                } else {
                    doOptimize = true;
                }
            }

            if(doOptimize) {
                try {
                    BaseNode opt = optimize(safe, elements);
                    if(opt != null) {
                        replace.setOptimized(opt);
                        addReference(replace.getSourceFile(), opt);
                        return false;
                    }
                } catch (Exception ex) {
                    return true;
                }
            }
            return true;
        }

        private BaseNode optimize(boolean safe, List<BaseNode> elements) {
            StringBuilder buff = new StringBuilder();
            ObjectLiteral config = null;

            for(BaseNode element : elements) {
                element = element.getOptimized();
                if (element instanceof StringLiteral) {
                    buff.append(((StringLiteral) element).getValue());
                } else if (element instanceof ObjectLiteral) {
                    if(config == null) {
                        config = (ObjectLiteral)element;
                        continue;
                    } else {
                        // cannot optimize, as don't know what to do with the extra
                        // object;
                        return null;
                    }
                } else if (element instanceof com.sencha.tools.compiler.ast.js.PassthroughNode) {
                    com.sencha.tools.compiler.ast.js.PassthroughNode passthroughNode = (com.sencha.tools.compiler.ast.js.PassthroughNode) element;
                    if ("ObjectLiteralExpressionTree".equals(passthroughNode.getTreeType())) {
                        // Handle PassthroughNode representing ObjectLiteralExpressionTree
                        // Cannot optimize PassthroughNode, so return null
                        return null;
                    } else {
                        // cannot optimize
                        return null;
                    }
                } else {
                    // cannot optimize
                    return null;
                }
            }

            if(buff.length() == 0) {
                return null;
            }

            String html = buff.toString();
            ObjectLiteral newConfig = createConfig(html, config);
            String escaped = "''";
            if(safe) {
                StringLiteral lit = new StringLiteral();
                lit.setQuoteCharacter('\'');
                lit.setValue(html);
                escaped = AstUtil.toSource(lit);
            }
            String newSource = StringUtil.formatString(
                "new Ext.XTemplate(%s, %s)",
                escaped,
                AstUtil.toSource(newConfig));

            return AstUtil.getFirstNode(newSource, BaseNode.class);
        }

        private ObjectLiteral createConfig(String tpl, ObjectLiteral config) {
            ObjectLiteral newConfig = new ObjectLiteral();
            if(config != null) {
                for(ObjectProperty prop : config.getElements()) {
                    newConfig.addElement(prop);
                }
            }

            String fn = null;
            if(config != null) {
                fn = _host.call(String.class, "getTplFn", tpl, AstUtil.toSource(config));
            } else {
                fn = _host.call(String.class, "getTplFn", tpl);
            }
            fn = StringUtil.formatString("(function(){%s})()", fn);
            newConfig.addElement(AstUtil.createProperty("compiled", true));
            RootNode root = AstUtil.parse(fn);
            BaseNode node = root.getElements().get(0);
            if(node instanceof ExpressionStatement) {
                node = ((ExpressionStatement) node).getExpression();
            }
            newConfig.addElement(AstUtil.createProperty("fn", node));
            return newConfig;
        }

        private void addReference(SourceFile sf, BaseNode node) {
            if(sf != null) {
                ReferenceVisitor vis = new ReferenceVisitor();
                vis.setContext((SymbolCache) sf.getScope().getScopeSymbols());
                vis.setCurrentFile(sf);
                vis.visit(node);
            }
        }

        private Tag getTag(BaseNode node) {
            Tag tag = node.getOptionalTag(
                "optimize.xtemplate",
                PrecompileTag);
            return tag;
        }

    };

    @Override
    public void optimizeFile(SourceFile sourceFile, CompilerContext context) {
        _vis.setContext(context);
        _vis.visit(sourceFile.getAstRoot());
    }

}
