/*
 * Copyright (c) 2012-2013. Sencha Inc.
 */
package com.sencha.tools.compiler.sources;

import com.sencha.logging.SenchaLogManager;
import com.sencha.tools.compiler.CompilerMessage;
import com.sencha.tools.compiler.ast.js.*;
import org.slf4j.Logger;

import java.util.List;

import static com.sencha.tools.compiler.ast.AstUtil.resolveName;

public abstract class BaseDefineVisitor<T> extends CommentNodevisitor<T> {
    private static final Logger _logger = SenchaLogManager.getLogger();

    private SourceFile _currentFile;
    protected boolean _isOnCreated = false;
    
    protected boolean processDefineFunction (FunctionNode func, StringLiteral className,
                                             T symbols) {

        List<BaseNode> elements = ((Block)func.getBody()).getElements();
        BaseNode last = elements.get(elements.size() - 1);

        if (last instanceof ReturnStatement) {
            BaseNode value = ((ReturnStatement) last).getReturnValue();

            if (value instanceof ObjectLiteral) {
                return processDefineObject((ObjectLiteral) value, className, symbols);
            } else if (value instanceof com.sencha.tools.compiler.ast.js.PassthroughNode) {
                // Handle PassthroughNode for fallback compatibility
                // When ObjectLiteralExpressionTree converter is disabled, returned object literals become PassthroughNodes
                // Skip processing but don't log error - this allows fallback to Closure Compiler
                return true;
            } else {
                CompilerMessage.UnsupportedExtDefineFormat.log(func,
                    "function does not return object literal");
            }
        } else {
            CompilerMessage.UnsupportedExtDefineFormat.log(func);
        }

        return true;
    }

    protected abstract boolean processDefineObject (ObjectLiteral obj,
                                                    StringLiteral className,
                                                    T scope);

    protected boolean processExtDefine (FunctionCall node, T symbols) {

        boolean keepGoing = true;
        List<BaseNode> args = node.getArguments();
        
        if (!(args.get(0) instanceof StringLiteral)) {
            return keepGoing;
        }
        
        
        StringLiteral className = (StringLiteral) args.get(0);
        BaseNode define = args.get(1);
        BaseNode onCreated = null;
        
        if(args.size() > 2) {
            onCreated = args.get(2);
        }
        
        // _base literal style define - we can process this one...
        if (define instanceof ObjectLiteral) {

            ObjectLiteral obj = (ObjectLiteral) define;
            keepGoing = processDefineObject(obj, className, symbols);

        } else if (define instanceof com.sencha.tools.compiler.ast.js.PassthroughNode) {
            // Handle PassthroughNode for fallback compatibility
            // When ObjectLiteralExpressionTree converter is disabled, object literals become PassthroughNodes
            // Skip processing but don't log error - this allows fallback to Closure Compiler
            keepGoing = true;
        
        } else if (define instanceof ParenthesizedExpression) {

            ParenthesizedExpression expr = (ParenthesizedExpression) define;
            BaseNode decl = expr.getExpression();
            if (decl instanceof FunctionNode) {
                keepGoing = processDefineFunction((FunctionNode) decl, className, symbols);
            } else if (decl instanceof FunctionCall) {
                BaseNode target = ((FunctionCall) decl).getTarget();
                if (target instanceof FunctionNode) {
                    keepGoing = processDefineFunction(
                        (FunctionNode) target, className, symbols);
                }
            } else {
                CompilerMessage.UnsupportedExtDefineFormat.log(node);
            }

        } else if (define instanceof FunctionCall) {

            FunctionCall call = (FunctionCall) define;
            BaseNode target = call.getTarget();
            if(target instanceof FunctionNode) {
                FunctionNode funcNode = (FunctionNode) target;
                processDefineFunction(funcNode, className, symbols);
            } else if (target instanceof ParenthesizedExpression) {
                ParenthesizedExpression expr = (ParenthesizedExpression) target;
                BaseNode decl = expr.getExpression();
                if (decl instanceof FunctionNode) {
                    keepGoing = processDefineFunction((FunctionNode) decl, className, symbols);
                }
            } else {
                // Unit tests often use a function that returns an object
                CompilerMessage.UnsupportedExtDefineFormatDebug.log(node);
            }

        } else if(define instanceof FunctionNode) {
            keepGoing = processDefineFunction((FunctionNode)define, className, symbols);
        } else {
            // See above
            CompilerMessage.UnsupportedExtDefineFormatDebug.log(node);
        }
        
        if(onCreated != null) {
            if(!keepGoing) {
                _isOnCreated = true;
                visit(onCreated);
                _isOnCreated = false;
            }
        }
        
        return keepGoing;
    }

    boolean processExtExtend (FunctionCall node, T symbols) {
//        List<BaseNode> args = node.getArguments();
//        int len = args.size();
//        if (len == 3) {
//
//        }
        return true;
    }

    @Override
    public void onFunctionCall(FunctionCall node) {
        String name = resolveName(node.getTarget());
        if ("Ext.define".equals(name)) {
            if(processExtDefine(node, getContext())) {
                super.onFunctionCall(node);
            }
        } else if ("Ext.extend".equals(name)) {
            if(processExtExtend(node, getContext())) {
                super.onFunctionCall(node);
            }
        } else {
            super.onFunctionCall(node);
        }
    }

    public SourceFile getCurrentFile() {
        return _currentFile;
    }

    public void setCurrentFile(SourceFile currentFile) {
        _currentFile = currentFile;
    }
}
