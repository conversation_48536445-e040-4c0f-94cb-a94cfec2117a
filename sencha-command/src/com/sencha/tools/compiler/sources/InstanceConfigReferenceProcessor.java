package com.sencha.tools.compiler.sources;

import com.sencha.exceptions.ExBuild;
import com.sencha.logging.SenchaLogManager;
import com.sencha.tools.compiler.CompilerMessage;
import com.sencha.tools.compiler.ast.AstUtil;
import com.sencha.tools.compiler.ast.js.*;
import com.sencha.tools.compiler.sources.types.AutoDependency;
import com.sencha.tools.compiler.sources.types.ClassDefinition;
import com.sencha.util.ObjectUtil;
import com.sencha.util.Pair;
import com.sencha.util.StringUtil;
import com.sencha.util.functional.Action;
import com.sencha.util.functional.Func;
import org.slf4j.Logger;

import java.util.*;
import java.util.Map.Entry;

import static com.sencha.tools.compiler.ast.AstUtil.getObjectProperties;
import static com.sencha.tools.compiler.ast.AstUtil.resolveName;
import static com.sencha.util.CollectionUtil.wrap;
import static com.sencha.util.StringUtil.isNullOrEmpty;

class InstanceConfigReferenceProcessor {
    private static final Logger _logger = SenchaLogManager.getLogger();
    
    private ReferenceVisitor _visitor;
    
    private static final Map<String, String> _propNameMap = 
            new LinkedHashMap<String, String>();
    
    private static final Map<String, ReferenceType> _refTypeMap = 
            new LinkedHashMap<String, ReferenceType>();
    
    private final Stack<InstanceConfig> _configStack = 
        new Stack<InstanceConfig>();
    
    static {
        _propNameMap.put("xclass", "");
        _propNameMap.put("xtype", "widget.");
        _propNameMap.put("ftype", "feature.");
        _propNameMap.put("ptype", "plugin.");
        
        _refTypeMap.put("xclass", ReferenceType.XClassReference);
        _refTypeMap.put("xtype", ReferenceType.XTypeReference);
        _refTypeMap.put("ftype", ReferenceType.FTypeReference);
        _refTypeMap.put("ptype", ReferenceType.PTypeReference);
    }
    
    public InstanceConfigReferenceProcessor(ReferenceVisitor visitor) {
        _visitor = visitor;
    }

    private Pair<String, BaseNode> detectClassName(InstanceConfig config, SourceFile file) {
        
        Map<String, InstanceConfig.InstanceConfigMember> props = config.getValues();
        String propName = null;
        for(String prop : _propNameMap.keySet()) {
            if(props.containsKey(prop)) {
                propName = prop;
                break;
            }
        }
        InstanceConfig.InstanceConfigMember member = props.get(propName);
        BaseNode propVal = member != null ? member.getNode() : null;
        String rhs = AstUtil.resolveName(propVal);
        if(!StringUtil.isNullOrEmpty(rhs)) {
            String cname = StringUtil.formatString("%s%s", _propNameMap.get(propName), rhs);
            // cannot ensure the requirement here, as this may be from an
            // auto dep that isn't blaming a class body
//            _visitor.ensureCurrentFileHasRequirement(
//                    cname, 
//                    (SymbolCache)file.getScope().getScopeSymbols(), 
//                    config.getOriginalValue(), 
//                    _refTypeMap.get(propName));
            return new Pair<String, BaseNode>(cname, propVal);
            

        } else if(!StringUtil.isNullOrEmpty(propName) && _logger.isDebugEnabled()){
            int lineNo = -1;
            int position = -1;
            if(propVal != null) {
                lineNo = propVal.getLine();
                position = propVal.getPosition();
            }
            Object[] debugProps = new Object[]{
                propName,
                file.getCanonicalPath(),
                lineNo,
                position
            };
            _logger.debug(
                "found property named '{}' at {} : {} : {}, but value was not convertable to a string",
                debugProps);
        }
        return null;
    }


    private Stack<ClassDefinition> _loopDefs = new Stack<ClassDefinition>();

    private Collection<ClassDefinition> getClassDefinitions(
            String className,
            ClassPathScopeSymbols symbols)
    {
        Set<ClassDefinition> definitions = new LinkedHashSet<ClassDefinition>();
        ClassDefinition def = symbols.getClassForName(className);
        if(_loopDefs.contains(def)) {
            String message = wrap(_loopDefs)
                    .transform(new Func<ClassDefinition, String>() {
                        @Override public String map(ClassDefinition classDefinition) {
                            return classDefinition.getClassName() + " : " + classDefinition.getSourceFile().getCanonicalPath();
                        }
                    }).append(def.getClassName() + " : " + def.getSourceFile().getCanonicalPath()).join(" -> \n");
            message = " \n" + message;
            _logger.error(message);
            _loopDefs.clear();
            throw new ExBuild(message);
        }
        _loopDefs.push(def);
        if(def != null) {
            for(ClassDefinition base : def.getAllBaseClasses()) {
                definitions.addAll(getClassDefinitions(base.getClassName(), symbols));
            }
            definitions.add(def);
        }
        _loopDefs.pop();
        return definitions;
    }

    public void processInstanceConfig(
        String defaultClassName,
        ObjectLiteral config,
        ObjectLiteral defaults,
        SourceFile file,
        boolean isClassBody,
        ClassPathScopeSymbols symbols)
    {
        processInstanceConfig(defaultClassName, config, defaults, file, isClassBody, null, symbols);
    }
    
    private void processInstanceConfig(
            String defaultClassName,
            ObjectLiteral config, 
            ObjectLiteral defaults, 
            SourceFile file,
            boolean isClassBody,
            AutoDependency dep,
            ClassPathScopeSymbols symbols)
    {
        InstanceConfig instConfig = new InstanceConfig(config, defaults);
        Pair<String, BaseNode> pair = detectClassName(instConfig, file);
        String instanceClassName = defaultClassName;
        BaseNode blameNode = null;
        if (pair != null) {
            instanceClassName = pair.getLeft();
            blameNode = pair.getRight();
        }
        if(!processInstanceConfigForClass(instanceClassName, config, defaults, file, isClassBody, dep, symbols, blameNode)) {
            for(ObjectProperty prop : config.getElements()) {
                _visitor.visit(prop.getRight());
            }
        }
    }

    private boolean processInstanceConfigForClass(
        String className,
        ObjectLiteral config,
        ObjectLiteral defaults,
        SourceFile file,
        boolean isClassBody,
        AutoDependency dep,
        ClassPathScopeSymbols symbols)
    {
        return processInstanceConfigForClass(className, config, defaults, file, isClassBody, dep, symbols, null);
    }

    private boolean processInstanceConfigForClass(
            String className, 
            ObjectLiteral config, 
            ObjectLiteral defaults, 
            SourceFile file,
            boolean isClassBody,
            AutoDependency dep,
            ClassPathScopeSymbols symbols,
            BaseNode blameNode)
    {
        
        if(StringUtil.isNullOrEmpty(className)) {
            return false;
        }
        
        String resolvedClassName = symbols.resolveClassName(className);
        ClassDefinition def = symbols.getClassForName(resolvedClassName);
        if(def == null) {
            return false;
        }
        
        if(dep != null) {
            addImplicitRequirement(
                resolvedClassName,
                className,
                file,
                ObjectUtil.defaultObject(blameNode, config),
                dep,
                isClassBody,
                symbols);
        } else {
            if(config.getClassDefinition() == null || !className.equals(config.getClassDefinition().getClassName())) {
                _visitor.ensureCurrentFileHasRequirement(className, (SymbolCache)symbols, ObjectUtil.defaultObject(blameNode, config), ReferenceType.Auto);
            }
        }

        for(InstanceConfig cfg : _configStack) {
            if(cfg.getOriginalValue() == config) {
                // already processing this instantiation, skip
                return true;
            }
        }

        InstanceConfig instConfig = new InstanceConfig(config, defaults, def);
        _configStack.push(instConfig);
        Collection<ClassDefinition> defs = getClassDefinitions(className, symbols);
        Map<String, List<AutoDependency>> autodeps = getAutoDependencies(defs, symbols);
        processAutoDependencies(instConfig, autodeps, symbols, file, isClassBody);
        _configStack.pop();
        return true;
    }
    
    
    private void processAutoDependencies(
            InstanceConfig config,
            Map<String, List<AutoDependency>> autodeps,
            ClassPathScopeSymbols symbols,
            SourceFile file,
            boolean isClassBody)
    {
        Map<String, InstanceConfig.InstanceConfigMember> props = config.getValues();
        for(Entry<String, InstanceConfig.InstanceConfigMember> entry : props.entrySet()) {
            String name = entry.getKey();
            InstanceConfig.InstanceConfigMember member = entry.getValue();
            BaseNode value = member.getNode();
            // If the property is a special name we know we need to handle,
            // process it here. Else, defer to the reference visitor
            if(autodeps.containsKey(name)) {
                for(AutoDependency dep : autodeps.get(name)) {
                    config.setCurrentDependency(dep);
                    processAutoDependency(dep, config, symbols, file, isClassBody);
                    config.setCurrentDependency(null);
                }
            } else if(member.isLocal()) {
                _visitor.visit(value);
            }
        }
    }
    
    private void processAutoDependency(
            final AutoDependency dep,
            final InstanceConfig instanceConfig,
            final ClassPathScopeSymbols symbols,
            final SourceFile file,
            final boolean isClassBody)
    {
        BaseNode prop = instanceConfig.getNode(dep.getPropertyName());

        if(prop != null && (prop instanceof ObjectLiteral ||
            (prop instanceof com.sencha.tools.compiler.ast.js.PassthroughNode &&
             "ObjectLiteralExpressionTree".equals(((com.sencha.tools.compiler.ast.js.PassthroughNode)prop).getTreeType())))) {
            if (prop instanceof ObjectLiteral) {
                Map<String, BaseNode> vals = AstUtil.getObjectProperties((ObjectLiteral)prop);
                if(vals.containsKey("$value")) {
                    prop = vals.get("$value");
                }
            }
            // For PassthroughNode representing ObjectLiteralExpressionTree, skip processing
        }

        if (prop == null) {
            if (!isNullOrEmpty(dep.getDefaultPropertyName())) {
                prop = instanceConfig.getNode(dep.getDefaultPropertyName());
            }
            if(prop != null && prop instanceof ObjectLiteral) {
                Map<String, BaseNode> vals = AstUtil.getObjectProperties((ObjectLiteral)prop);
                if(vals.containsKey("$value")) {
                    prop = vals.get("$value");
                }
            }
        }

        // if we don't have enough data to process this property, continue
        if (prop == null) {
            return;
        }

        String propVal = AstUtil.resolveName(prop);
        if (prop instanceof KeywordLiteral || "undefined".equals(propVal)) {
            // falsy values don't trigger auto dependencies
            if (StringUtil.isNullOrEmpty(propVal) ||
                    "false".equals(propVal) ||
                    "null".equals(propVal) ||
                    "undefined".equals(propVal)) {
                return;
            }
        }

        if (!StringUtil.isNullOrEmpty(dep.getDirectRef())) {
            _logger.debug("Setting direct ref {} to file {}", dep.getDirectRef(), file.getCanonicalPath());
            addImplicitRequirement(
                dep.getClassName(), 
                dep.getDirectRef(), 
                file, 
                prop,
                dep,
                isClassBody,
                symbols);
            return;
        }
        
        if(dep.getRequires() != null && dep.getRequires().size() > 0) {
            for(String ref : dep.getRequires()) {
                _logger.debug("Adding direct ref {} to file {}", ref, file.getCanonicalPath());
                addImplicitRequirement(
                    dep.getClassName(), 
                    ref, 
                    file, 
                    prop,
                    dep,
                    isClassBody,
                    symbols);
            }
        }
        
        if (prop instanceof StringLiteral) {
            String alias = resolveName(prop);
            String fullName = resolveReferenceName(alias, instanceConfig, dep, symbols);
            if(fullName != null) {
                addImplicitRequirement(
                    dep.getClassName(), 
                    fullName, 
                    file, 
                    prop,
                    dep,
                    isClassBody,
                    symbols);
                processInstanceConfigForClass(fullName, new ObjectLiteral(), null, file, isClassBody, dep, symbols, prop);
            }

        } else if (prop instanceof ObjectLiteral ||
                   (prop instanceof com.sencha.tools.compiler.ast.js.PassthroughNode &&
                    "ObjectLiteralExpressionTree".equals(((com.sencha.tools.compiler.ast.js.PassthroughNode)prop).getTreeType()))) {
            if (prop instanceof ObjectLiteral) {
                ObjectLiteral configObj = ((ObjectLiteral) prop);

            Action<ObjectLiteral> proc = new Action<ObjectLiteral>(){
                @Override public void process(ObjectLiteral config) {
                    Map<String, BaseNode> configProps = getObjectProperties(config);
                    String typeRef = dep.getTypeProperty();
                    BaseNode typeNode = configProps.get(typeRef);
                    String defaultTypeValue = dep.getDefaultType();
                    String alias = defaultTypeValue;

                    if (typeNode != null) {
                        alias = resolveName(typeNode);
                    }

                    if (StringUtil.isNullOrEmpty(alias)) {
                        if(_logger.isDebugEnabled()) {
                            CompilerMessage.GenericWarn.log(config,
                                "Could not resolve '" + typeRef + "' property from object literal");
                        }
                        return;
                    }

                    String resolvedName = resolveReferenceName(alias, instanceConfig, dep, symbols);
                    if(resolvedName != null) {
                        addImplicitRequirement(
                            dep.getClassName(), 
                            resolvedName, 
                            file, 
                            typeNode,
                            dep,
                            isClassBody,
                            symbols);
                        // recurse over the object literal
                        processInstanceConfigForClass(resolvedName, config, null, file, isClassBody, dep, symbols);
                    }

                }
            };

            if(dep.isKeyedObject()) {
                for(ObjectProperty configProp : configObj.getElements()) {
                    BaseNode rhs = configProp.getRight();
                    if(rhs instanceof ObjectLiteral) {
                        proc.process((ObjectLiteral)rhs);
                    } else if (rhs instanceof StringLiteral) {
                        String alias = resolveName(rhs);
                        String fullName = resolveReferenceName(alias, instanceConfig, dep, symbols);
                        if(fullName != null) {
                            addImplicitRequirement(
                                dep.getClassName(),
                                fullName,
                                file,
                                rhs,
                                dep,
                                isClassBody,
                                symbols);
                            processInstanceConfigForClass(fullName, new ObjectLiteral(), null, file, isClassBody, dep, symbols);
                        }
                    }
                }
            } else {
                proc.process(configObj);
            }
            }
            // For PassthroughNode representing ObjectLiteralExpressionTree, skip processing

        } else if (prop instanceof ArrayLiteral) {
            ArrayLiteral array = ((ArrayLiteral) prop);
            for (BaseNode node : array.getElements()) {
                if(node instanceof ObjectLiteral) {
                    BaseNode defName = instanceConfig.getNode(dep.getDefaultTypeProperty());
                    BaseNode defConfig = instanceConfig.getNode(dep.getDefaultProperty());
                    
                    String defaultName;
                    ObjectLiteral defaultConfig = null;

                    defaultName = symbols.resolveClassName(AstUtil.resolveName(defName));
                    if(!StringUtil.isNullOrEmpty(defaultName) && !symbols.getAllClasses().containsKey(defaultName)) {
                        defaultName = symbols.resolveClassName("widget." + AstUtil.resolveName(defName));
                    }

                    if(defConfig != null && defConfig instanceof ObjectLiteral) {
                        defaultConfig = (ObjectLiteral)defConfig;
                    }
                    
                    ObjectLiteral item = (ObjectLiteral)node;
                    
                    processInstanceConfig(
                        defaultName, 
                        item, 
                        defaultConfig, 
                        file,
                        isClassBody,
                        dep,
                        symbols);
                    
                } else if (node instanceof StringLiteral) {
                    String reqName = resolveName(node);

                    if (reqName != null) {
                        if("-".equals(reqName)) {
                            reqName = "widget.tbseparator";
                        } else if ("->".equals(reqName)) {
                            reqName = "widget.tbfill";
                        } else if (" ".equals(reqName)) {
                            reqName = "widget.tbspacer";
                        } else if ("".equals(reqName)) {
                            reqName = "widget.tbspacer";
                        }
                    }

                    String resolvedName = resolveReferenceName(reqName, instanceConfig, dep, symbols);
                    if(resolvedName != null) {
                        processInstanceConfigForClass(resolvedName, new ObjectLiteral(), null, file, isClassBody, dep, symbols);
                    }
                }
            }
        } else if ("true".equals(AstUtil.resolveName(prop))){
            String refName = dep.getDefaultType();
            if (!StringUtil.isNullOrEmpty(refName)) {
                ObjectLiteral cfg = new ObjectLiteral();
                String fullName = resolveReferenceName(refName, instanceConfig, dep, symbols);
                if(fullName != null) {
                    addImplicitRequirement(
                        dep.getClassName(),
                        fullName,
                        file,
                        prop,
                        dep,
                        isClassBody,
                        symbols);
                    processInstanceConfigForClass(fullName, cfg, null, file, isClassBody, dep, symbols);
                }
            }
        } else {
            _visitor.visit(prop);
        }
    }
    
    
    private Map<String, List<AutoDependency>> getAutoDependencies(Collection<ClassDefinition> classDefs, ClassPathScopeSymbols symbols) {
        Map<String, List<AutoDependency>> deps = new HashMap<String, List<AutoDependency>>();
        for(ClassDefinition def : classDefs) {
            for(Entry<String, AutoDependency> entry : wrap(symbols.getAutoDependencies(def).entrySet()).reverse()) {
                String key = entry.getKey();
                AutoDependency autodep = entry.getValue();
                if(!deps.containsKey(key)) {
                    deps.put(key, new ArrayList<AutoDependency>());
                }
                deps.get(key).add(autodep);
            }
        }
        return deps;
    }
    
    private void addImplicitRequirement(
        String className, 
        String name, 
        SourceFile file, 
        BaseNode node,
        AutoDependency dep,
        boolean isClassBody,
        ClassPathScopeSymbols symbols)
    {
        if (file == null) {
            _logger.error("file was null");
            return;
        }
        
        boolean addReference = false;
        
        if(isClassBody && dep.getReferenceTarget().isClassBodyReference()) {
            addReference = true;
        } else if(!isClassBody && dep.getReferenceTarget().isInstanceReference()) {
            addReference = true;
        }
        
        if(!addReference) {
            return;
        }

        if (_logger.isDebugEnabled()) {
            _logger.debug("Adding implicit dependency from class {} on name {} to file {}",
                new Object[]{ className, name, file.getCanonicalPath() });
        }

        Reference ref = new Reference(name, ReferenceType.Auto, file, node);
        ref.setActive(_visitor.getActive());
        symbols.addReference(file, ref);
    }
    
    private String getNamespaceFromSimpleClassName (String baseClassName) {
        return baseClassName
            .substring(baseClassName.lastIndexOf(".") + 1)
            .toLowerCase();
    }

    private String getPrefixFromClassName(String baseClassName, String namespace) {
        if(_logger.isTraceEnabled()) {
            _logger.trace("checking {} for name .{}.", baseClassName, namespace);
        }
        String substr = StringUtil.formatString(".%s.", namespace);
        int idx = baseClassName.indexOf(substr);
        if(idx > -1) {
            return baseClassName.substring(0, idx);
        }
        return null;
    }

    private String resolveReferenceName(
        String name,
        InstanceConfig instanceConfig,
        AutoDependency dep,
        ClassPathScopeSymbols symbols)
    {
        Set<String> names = getReferenceNames(name, instanceConfig, dep);
        String resolved = null;
        for(String n : names) {
            String resolvedName = symbols.resolveClassName(n);
            if(symbols.isNameDefined(resolvedName)) {
                if(resolved != null && !resolved.equals(resolvedName)) {
                    _logger.warn("Encountered multiple matches for name {} : {}, {}",
                        new Object[]{
                            name,
                            resolved,
                            resolvedName
                        });
                    return null;
                } else {
                    resolved = resolvedName;
                }
            }
        }
        return resolved;
    }
    
    private Set<String> getReferenceNames (
        String name,
        InstanceConfig instanceConfig,
        AutoDependency dep) 
    {
        Set<String> candidates = new HashSet<String>();
        candidates.add(name);
        
        String prefix = dep.getAliasPrefix() == null
            ? dep.getPropertyName()
            : dep.getAliasPrefix();

        String rootName = getRootClassName(instanceConfig);
        String root = getAppName(instanceConfig, dep);
        
        if(isNullOrEmpty(root)){
            root = rootName.contains(".")
                ? rootName.substring(0, rootName.indexOf("."))
                : rootName;
        }

        if(!StringUtil.isNullOrEmpty(prefix)) {
            candidates.add(prefix + "." + name);  
        }
        
        if(dep.isMvc()) {
            if(name.contains("@")) {
                String[] parts = name.split("@");
                String fullName = parts[1] + "." + parts[0];
                candidates.add(fullName);
            } else {
                String mvcPrefix = prefix;
                if(!StringUtil.isNullOrEmpty(dep.getMvcPrefix())) {
                    mvcPrefix = dep.getMvcPrefix();
                }
                candidates.add(root + "." + mvcPrefix + "." + name);
            }
        }
        
        if(dep.isProfile()) {
            String namespace = getNamespaceFromSimpleClassName(instanceConfig.getClassName());

            BaseNode nsNode = instanceConfig.getNode("namespace");

            if(nsNode instanceof StringLiteral) {
                String nsVal = ((StringLiteral)nsNode).getValue();
                if(!"auto".equals(nsVal)) {
                    namespace = nsVal;
                }
            }
            String profilePrefix = prefix;
            if(!StringUtil.isNullOrEmpty(dep.getMvcPrefix())) {
                profilePrefix = dep.getMvcPrefix();
            }
            candidates.add(StringUtil.join(new String[]{
                root,
                profilePrefix,
                namespace,
                name
            }, "."));
        }

        HashSet cleaned = new HashSet();
        for(String candidate : candidates) {
            cleaned.add(candidate.replace("..", "."));
        }
        return cleaned;
    }
    
    private String getRootClassName(InstanceConfig instanceConfig) {
        String rootClassName = null;
        if(instanceConfig != null) {
            rootClassName = instanceConfig.getClassName();
        }
        if(!_configStack.isEmpty()) {
            rootClassName = _configStack.get(0).getClassName();
        }
        return rootClassName;
    }
    
    private String getAppName(InstanceConfig instanceConfig, AutoDependency dep) {
        String className = dep.getClassName();
        String appName = instanceConfig.getDetectedApplicatioName();
        String rootName = getRootClassName(instanceConfig);
        
        if (isNullOrEmpty(appName) &&
            instanceConfig.getClassDefinition() != null &&
            instanceConfig.getClassDefinition().isInstanceOf("Ext.app.Controller"))
        {
            appName = resolveName(instanceConfig.getNode("name"));
        }

        if(isNullOrEmpty(appName)) {
            for(InstanceConfig cfg : _configStack) {
                _logger.debug(
                    "checking config for type {} for app name",
                    cfg.getClassName());
                
                if(!isNullOrEmpty(appName = cfg.getDetectedApplicatioName())) {
                    break;
                }
            }
        }

        if(isNullOrEmpty(appName) && !_configStack.isEmpty()) {
            appName = getPrefixFromClassName(
                rootName, 
                getNamespaceFromSimpleClassName(className));
        }

        if (isNullOrEmpty(appName)) {
            // this is the base class (Ext.app.Controller)
            // so look for ^(some.package).controller
            // from the startingClassName (MyApp.package.controller.Main)
            // => MyApp.package is what we need
            appName = getPrefixFromClassName(getRootClassName(instanceConfig),
                getNamespaceFromSimpleClassName(className));
        }
        return appName;
    }
}
