/*
 * Copyright (c) 2012-2013. Sencha Inc.
 */
package com.sencha.tools.compiler.sources.types;

import com.sencha.tools.compiler.ast.AstUtil;
import com.sencha.tools.compiler.ast.js.*;
import com.sencha.tools.compiler.sources.ClassPathScopeSymbols;

import java.util.ArrayList;
import java.util.List;


public final class ClassDefinitionHelper {

    public ClassDefinitionHelper(String clsName, BaseNode config, ClassPathScopeSymbols scope) {
        _className = clsName;
        _config = config;
        _symbols = scope;
        processObjLiteral(detectConfigObj(_config));
   }
    
    public ObjectLiteral getFilteredConfig (String... filters) {
        List<ObjectProperty> filteredProps = new ArrayList<ObjectProperty>();
        for (ObjectProperty prop : getOriginalDefinition().getElements()) {
            BaseNode left = prop.getLeft();
            String propName;
            if (left instanceof Name) {
                propName = ((Name) left).getValue();
            } else {
                propName = AstUtil.cast(left, StringLiteral.class).getValue();
            }
            boolean skip = false;
            for (String name : filters) {
                if (propName.equals(name)) {
                    skip = true;
                    break;
                }
            }
            if (!skip) {
                filteredProps.add(prop);
            }
        }
        ObjectLiteral obj = new ObjectLiteral();
        obj.setElements(filteredProps);
        return obj;
    }
    
    public void processObjLiteral (ObjectLiteral obj) {
        setOriginalDefinition(obj);
        _members = new ObjectLiteral();

        List<ObjectProperty> instMembers = new ArrayList<ObjectProperty>();

        for (ObjectProperty prop : obj.getElements()) {

            String name;
            BaseNode nameNode = prop.getLeft();
            name = AstUtil.resolveName(nameNode);

            if ("extend".equals(name)) {
                _extend = prop;
            } else if ("mixins".equals(name)) {
                _mixinsDef = prop;
            //} else if ("requires".equals(name)) {
            //    requires = prop;
            //} else if ("uses".equals(name)) {
            //    uses = prop;
            //} else if ("override".equals(name)) {
            //    override = prop;
            } else if ("statics".equals(name)) {
                _statics = prop;
                instMembers.add(prop);
            } else if ("inheritableStatics".equals(name)) {
                _inheritableStatics = prop;
                instMembers.add(prop);
            } else if ("alternateClassName".equals(name)) {
                _alternates = prop;
                instMembers.add(prop);
            } else if ("alias".equals(name)) {
                _alias = prop;
            } else if ("singleton".equals(name)) {
                _singleton = prop;
                instMembers.add(prop);
            } else if ("xtype".equals(name)) {
                // skip xtypes
            } else {
                instMembers.add(prop);
            }
        }

        getMembers().setElements(instMembers);
    }

    private ObjectLiteral detectConfigObj (BaseNode config) {
        FunctionNode functionNode;
        ObjectLiteral objLiteral = null;
        
        if (config instanceof ObjectLiteral) {
            objLiteral = (ObjectLiteral) config;

            processObjLiteral(objLiteral);
        } else if (config instanceof com.sencha.tools.compiler.ast.js.PassthroughNode) {
            com.sencha.tools.compiler.ast.js.PassthroughNode passthroughNode = (com.sencha.tools.compiler.ast.js.PassthroughNode) config;
            if ("ObjectLiteralExpressionTree".equals(passthroughNode.getTreeType())) {
                // Handle PassthroughNode representing ObjectLiteralExpressionTree
                // Skip processing for fallback compatibility
                objLiteral = null;
            }
        } else {
            functionNode = ClassDefinition.getConfigFunctionNode(config);
            
            if (functionNode != null) {
                objLiteral = ClassDefinition.getFunctionReturnLiteral(functionNode);
                
                processObjLiteral(objLiteral);
                
                _isFunctionConfig = true;
            }
        }
        return objLiteral;
    }
    
    
    public ObjectLiteral getOriginalDefinition () {
        if(_originalDefinition != null) {
            return _originalDefinition;
        }
        return getClassDefinition().getConfigObj();
    }

    public void setOriginalDefinition (ObjectLiteral originalDefinition) {
        _originalDefinition = originalDefinition;
    }

    public ObjectProperty getExtend () {
        return _extend;
    }

    public ObjectProperty getStatics () {
        return _statics;
    }

    public ObjectProperty getInheritableStatics () {
        return _inheritableStatics;
    }

    public ObjectProperty getAlternates () {
        return _alternates;
    }

    public ObjectProperty getAlias () {
        return _alias;
    }

    public ObjectProperty getMixinsDef () {
        return _mixinsDef;
    }

    public ObjectProperty getSingleton () {
        return _singleton;
    }

    public ObjectProperty getOnClassExtended () {
        return _onClassExtended;
    }

    public ObjectLiteral getMembers () {
        return _members;
    }

    public ClassDefinition getClassDefinition() {
        if(_classDef == null) {
            _classDef = _symbols.getClassForName(getClassName());
        }
        return _classDef;
    }
    
    public String getClassName() {
        return _className;
    }
    
    public boolean isFunctionConfig() {
        return _isFunctionConfig;
    }
    
    public boolean requiresAsync() {
        if(getClassDefinition() != null) {
            return getClassDefinition().requiresAsync();
        }
        return false;
    }

    private ObjectLiteral _originalDefinition;

    private ObjectProperty _extend;
    private ObjectProperty _mixinsDef;
    private ObjectProperty _statics;
    private ObjectProperty _inheritableStatics;
    private ObjectProperty _alternates;
    private ObjectProperty _alias;
    private ObjectProperty _singleton;
    private ObjectProperty _onClassExtended;

    private boolean _isFunctionConfig;
    private ObjectLiteral _members;
    private ClassDefinition _classDef;
    private String _className;
    private BaseNode _config;
    private ClassPathScopeSymbols _symbols;
}
