/*
 * Copyright (c) 2012-2016. Sencha Inc.
 */

package com.sencha.tools.compiler.ast;

import com.google.javascript.jscomp.parsing.parser.trees.*;
import com.sencha.logging.SenchaLogManager;
import com.sencha.tools.compiler.ast.js.BaseNode;
import org.slf4j.Logger;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;

public class ClosureConverter {

    private static final Logger _logger = SenchaLogManager.getLogger();

    // Set of ES6+ feature classes that should be excluded from ES5-only converter
    private static final Set<Class> _es6PlusFeatures = new HashSet<Class>() {{
        // ES6+ Classes
        add(ClassDeclarationTree.class);
        add(GetAccessorTree.class);
        add(SetAccessorTree.class);
        add(ComputedPropertyDefinitionTree.class);
        add(ComputedPropertyGetterTree.class);
        add(ComputedPropertyMethodTree.class);
        add(ComputedPropertySetterTree.class);
        
        // ES6+ Arrow Functions & Parameters
        add(DefaultParameterTree.class);
        
        // ES6+ Template Literals
        add(TemplateLiteralExpressionTree.class);
        add(TemplateLiteralPortionTree.class);
        add(TemplateSubstitutionTree.class);
        
        // ES6+ Destructuring & Spread
        add(ArrayPatternTree.class);
        add(ObjectPatternTree.class);
        add(IterSpreadTree.class);
        
        // ES6+ Modules
        add(ExportDeclarationTree.class);
        add(ExportSpecifierTree.class);
        add(ImportDeclarationTree.class);
        add(ImportSpecifierTree.class);
        
        // ES6+ Loops
        add(ForOfStatementTree.class);
        
        // ES7+ Async/Await
        add(AwaitExpressionTree.class);
        add(ForAwaitOfStatementTree.class);
        
        // ES8+ Rest Parameters
        add(IterRestTree.class);
        add(ObjectRestTree.class);
        add(ObjectSpreadTree.class);
        
        // ES11+ Dynamic Imports & Meta
        add(DynamicImportTree.class);
        add(ImportMetaExpressionTree.class);
        
        // ES13+ Class Fields
        add(FieldDeclarationTree.class);
        
        // Other modern features
        add(NewTargetExpressionTree.class);
        add(YieldExpressionTree.class);
    }};

    private static Map<Class, ClosureNodeConverter> _converterMap = new HashMap<Class, ClosureNodeConverter>() {{
//        put(AmbientDeclarationTree.class, new ClosureNodeConverter() {
//            @Override
//            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
//                return converter.convert((AmbientDeclarationTree) tree);
//            }
//        });

        put(ArgumentListTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ArgumentListTree) tree);
            }
        });

        put(ArrayLiteralExpressionTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ArrayLiteralExpressionTree) tree);
            }
        });

        // put(ArrayPatternTree.class, new ClosureNodeConverter() {
        //     @Override
        //     public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
        //         return converter.convert((ArrayPatternTree) tree);
        //     }
        // });

//        put(ArrayTypeTree.class, new ClosureNodeConverter() {
//            @Override
//            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
//                return converter.convert((ArrayTypeTree) tree);
//            }
//        });

//        put(AssignmentRestElementTree.class, new ClosureNodeConverter() {
//            @Override
//            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
//                return converter.convert((AssignmentRestElementTree) tree);
//            }
//        });

        put(AwaitExpressionTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((AwaitExpressionTree) tree);
            }
        });

        put(BinaryOperatorTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((BinaryOperatorTree) tree);
            }
        });

        put(BlockTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((BlockTree) tree);
            }
        });

        put(BreakStatementTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((BreakStatementTree) tree);
            }
        });

        put(CallExpressionTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((CallExpressionTree) tree);
            }
        });

//        put(CallSignatureTree.class, new ClosureNodeConverter() {
//            @Override
//            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
//                return converter.convert((CallSignatureTree) tree);
//            }
//        });

        put(CaseClauseTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((CaseClauseTree) tree);
            }
        });

        put(CatchTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((CatchTree) tree);
            }
        });

        put(ClassDeclarationTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ClassDeclarationTree) tree);
            }
        });

        put(CommaExpressionTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((CommaExpressionTree) tree);
            }
        });

        put(ComprehensionForTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ComprehensionForTree) tree);
            }
        });

        put(ComprehensionIfTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ComprehensionIfTree) tree);
            }
        });

        put(ComprehensionTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ComprehensionTree) tree);
            }
        });

        put(ComputedPropertyDefinitionTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ComputedPropertyDefinitionTree) tree);
            }
        });

        put(ComputedPropertyGetterTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ComputedPropertyGetterTree) tree);
            }
        });

//        put(ComputedPropertyMemberVariableTree.class, new ClosureNodeConverter() {
//            @Override
//            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
//                return converter.convert((ComputedPropertyMemberVariableTree) tree);
//            }
//        });

        put(ComputedPropertyMethodTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ComputedPropertyMethodTree) tree);
            }
        });

        put(ComputedPropertySetterTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ComputedPropertySetterTree) tree);
            }
        });

        put(ConditionalExpressionTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ConditionalExpressionTree) tree);
            }
        });

        put(ContinueStatementTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ContinueStatementTree) tree);
            }
        });

        put(DebuggerStatementTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((DebuggerStatementTree) tree);
            }
        });

        put(DefaultClauseTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((DefaultClauseTree) tree);
            }
        });

        put(DefaultParameterTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((DefaultParameterTree) tree);
            }
        });

        put(DoWhileStatementTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((DoWhileStatementTree) tree);
            }
        });

        put(DynamicImportTree.class, new ClosureNodeConverter() { // New
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((DynamicImportTree) tree);
            }
        });

        put(EmptyStatementTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((EmptyStatementTree) tree);
            }
        });

//        put(EnumDeclarationTree.class, new ClosureNodeConverter() {
//            @Override
//            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
//                return converter.convert((EnumDeclarationTree) tree);
//            }
//        });

        put(ExportDeclarationTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ExportDeclarationTree) tree);
            }
        });

        put(ExportSpecifierTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ExportSpecifierTree) tree);
            }
        });

        put(ExpressionStatementTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ExpressionStatementTree) tree);
            }
        });

        put(FieldDeclarationTree.class, new ClosureNodeConverter() { // New
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((FieldDeclarationTree) tree);
            }
        });

        put(FinallyTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((FinallyTree) tree);
            }
        });

        put(ForAwaitOfStatementTree.class, new ClosureNodeConverter() { // New
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ForAwaitOfStatementTree) tree);
            }
        });

        put(ForInStatementTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ForInStatementTree) tree);
            }
        });

        put(ForOfStatementTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ForOfStatementTree) tree);
            }
        });

        put(ForStatementTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ForStatementTree) tree);
            }
        });

        put(FormalParameterListTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((FormalParameterListTree) tree);
            }
        });

        put(FunctionDeclarationTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((FunctionDeclarationTree) tree);
            }
        });

//        put(FunctionTypeTree.class, new ClosureNodeConverter() {
//            @Override
//            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
//                return converter.convert((FunctionTypeTree) tree);
//            }
//        });

//        put(GenericTypeListTree.class, new ClosureNodeConverter() {
//            @Override
//            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
//                return converter.convert((GenericTypeListTree) tree);
//            }
//        });

        put(GetAccessorTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((GetAccessorTree) tree);
            }
        });

        put(IdentifierExpressionTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((IdentifierExpressionTree) tree);
            }
        });

        put(IfStatementTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((IfStatementTree) tree);
            }
        });

        put(ImportDeclarationTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ImportDeclarationTree) tree);
            }
        });

        put(ImportMetaExpressionTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ImportMetaExpressionTree) tree);
            }
        });

        put(ImportSpecifierTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ImportSpecifierTree) tree);
            }
        });

        put(IterRestTree.class, new ClosureNodeConverter() { // New
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((IterRestTree) tree);
            }
        });

        put(IterSpreadTree.class, new ClosureNodeConverter() { // New
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((IterSpreadTree) tree);
            }
        });

//        put(IndexSignatureTree.class, new ClosureNodeConverter() {
//            @Override
//            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
//                return converter.convert((IndexSignatureTree) tree);
//            }
//        });

//        put(InterfaceDeclarationTree.class, new ClosureNodeConverter() {
//            @Override
//            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
//                return converter.convert((InterfaceDeclarationTree) tree);
//            }
//        });

        put(LabelledStatementTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((LabelledStatementTree) tree);
            }
        });

        put(LiteralExpressionTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((LiteralExpressionTree) tree);
            }
        });

        put(MemberExpressionTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((MemberExpressionTree) tree);
            }
        });

        put(MemberLookupExpressionTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((MemberLookupExpressionTree) tree);
            }
        });

//        put(MemberVariableTree.class, new ClosureNodeConverter() {
//            @Override
//            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
//                return converter.convert((MemberVariableTree) tree);
//            }
//        });

        put(MissingPrimaryExpressionTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((MissingPrimaryExpressionTree) tree);
            }
        });

//        put(NamespaceDeclarationTree.class, new ClosureNodeConverter() {
//            @Override
//            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
//                return converter.convert((NamespaceDeclarationTree) tree);
//            }
//        });

//        put(NamespaceNameTree.class, new ClosureNodeConverter() {
//            @Override
//            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
//                return converter.convert((NamespaceNameTree) tree);
//            }
//        });

        put(NewExpressionTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((NewExpressionTree) tree);
            }
        });

        put(NewTargetExpressionTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((NewTargetExpressionTree) tree);
            }
        });

        put(NullTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((NullTree) tree);
            }
        });

        // put(ObjectLiteralExpressionTree.class, new ClosureNodeConverter() {
        //     @Override
        //     public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
        //         return converter.convert((ObjectLiteralExpressionTree) tree);
        //     }
        // });

        // put(ObjectPatternTree.class, new ClosureNodeConverter() {
        //     @Override
        //     public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
        //         return converter.convert((ObjectPatternTree) tree);
        //     }
        // });

        put(ObjectRestTree.class, new ClosureNodeConverter() { // New
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ObjectRestTree) tree);
            }
        });

        put(ObjectSpreadTree.class, new ClosureNodeConverter() { // New
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ObjectSpreadTree) tree);
            }
        });

        // put(OptChainCallExpressionTree.class, new ClosureNodeConverter() { // New
        //     @Override
        //     public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
        //         return converter.convert((OptChainCallExpressionTree) tree);
        //     }
        // });

        // put(OptionalMemberExpressionTree.class, new ClosureNodeConverter() { // New
        //     @Override
        //     public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
        //         return converter.convert((OptionalMemberExpressionTree) tree);
        //     }
        // });

        // put(OptionalMemberLookupExpressionTree.class, new ClosureNodeConverter() { // New
        //     @Override
        //     public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
        //         return converter.convert((OptionalMemberLookupExpressionTree) tree);
        //     }
        // });

//        put(OptionalParameterTree.class, new ClosureNodeConverter() {
//            @Override
//            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
//                return converter.convert((OptionalParameterTree) tree);
//            }
//        });

//        put(ParameterizedTypeTree.class, new ClosureNodeConverter() {
//            @Override
//            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
//                return converter.convert((ParameterizedTypeTree) tree);
//            }
//        });

        put(ParenExpressionTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ParenExpressionTree) tree);
            }
        });

        put(ParseTree.class, new ClosureNodeConverter() { // New
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ParseTree) tree);
            }
        });

        put(ProgramTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ProgramTree) tree);
            }
        });

        put(PropertyNameAssignmentTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((PropertyNameAssignmentTree) tree);
            }
        });

//        put(RecordTypeTree.class, new ClosureNodeConverter() {
//            @Override
//            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
//                return converter.convert((RecordTypeTree) tree);
//            }
//        });

//        put(RestParameterTree.class, new ClosureNodeConverter() {
//            @Override
//            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
//                return converter.convert((RestParameterTree) tree);
//            }
//        });

        put(ReturnStatementTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ReturnStatementTree) tree);
            }
        });

        put(SetAccessorTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((SetAccessorTree) tree);
            }
        });

//        put(SpreadExpressionTree.class, new ClosureNodeConverter() {
//            @Override
//            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
//                return converter.convert((SpreadExpressionTree) tree);
//            }
//        });

        put(SuperExpressionTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((SuperExpressionTree) tree);
            }
        });

        put(SwitchStatementTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((SwitchStatementTree) tree);
            }
        });

        put(TemplateLiteralExpressionTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((TemplateLiteralExpressionTree) tree);
            }
        });

        put(TemplateLiteralPortionTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((TemplateLiteralPortionTree) tree);
            }
        });

        put(TemplateSubstitutionTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((TemplateSubstitutionTree) tree);
            }
        });

        put(ThisExpressionTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ThisExpressionTree) tree);
            }
        });

        put(ThrowStatementTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((ThrowStatementTree) tree);
            }
        });

        put(TryStatementTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((TryStatementTree) tree);
            }
        });

//        put(TypeAliasTree.class, new ClosureNodeConverter() {
//            @Override
//            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
//                return converter.convert((TypeAliasTree) tree);
//            }
//        });

//        put(TypeNameTree.class, new ClosureNodeConverter() {
//            @Override
//            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
//                return converter.convert((TypeNameTree) tree);
//            }
//        });

//        put(TypeQueryTree.class, new ClosureNodeConverter() {
//            @Override
//            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
//                return converter.convert((TypeQueryTree) tree);
//            }
//        });

//        put(TypedParameterTree.class, new ClosureNodeConverter() {
//            @Override
//            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
//                return converter.convert((TypedParameterTree) tree);
//            }
//        });

        put(UnaryExpressionTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((UnaryExpressionTree) tree);
            }
        });

//        put(UnionTypeTree.class, new ClosureNodeConverter() {
//            @Override
//            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
//                return converter.convert((UnionTypeTree) tree);
//            }
//        });

        put(UpdateExpressionTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((UpdateExpressionTree) tree);
            }
        });

        put(VariableDeclarationListTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((VariableDeclarationListTree) tree);
            }
        });

        put(VariableDeclarationTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((VariableDeclarationTree) tree);
            }
        });

        put(VariableStatementTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((VariableStatementTree) tree);
            }
        });

        put(WhileStatementTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((WhileStatementTree) tree);
            }
        });

        put(WithStatementTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((WithStatementTree) tree);
            }
        });

        put(YieldExpressionTree.class, new ClosureNodeConverter() {
            @Override
            public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
                return converter.convert((YieldExpressionTree) tree);
            }
        });
    }};

    private static final ClosureNodeConverter _noOp = new ClosureNodeConverter() {
        @Override
        public BaseNode convert(ParseTree tree, ClosureASTConverter converter) {
            return null;
        }
    };

    /**
     * Gets a converter for any ParseTree node type (original method)
     * @param tree The ParseTree to get a converter for
     * @return The corresponding ClosureNodeConverter, or null if not found
     */
    public ClosureNodeConverter getConverter(ParseTree tree) {
        ClosureNodeConverter convert = _converterMap.get(tree.getClass());
        // if (convert == null) {
        //     convert = _noOp;
        // }
        return convert;
    }

    /**
     * Gets a converter only for ES5 or below features. Returns null for ES6+ features.
     * This method is useful when you want to selectively handle only legacy JavaScript
     * features and skip modern syntax that might need special handling.
     * 
     * @param tree The ParseTree to get a converter for
     * @return The corresponding ClosureNodeConverter if it's an ES5 feature, null otherwise
     */
    public ClosureNodeConverter getConverterES5Only(ParseTree tree) {
        Class<?> treeClass = tree.getClass();

        // Check if this is an ES6+ feature
        if (_es6PlusFeatures.contains(treeClass)) {
            return null;
        }

        // Return the converter for ES5 features
        return _converterMap.get(treeClass);
    }
}