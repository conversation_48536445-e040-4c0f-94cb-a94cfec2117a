/*
 * Copyright (c) 2012-2016. Sencha Inc.
 */

package com.sencha.tools.compiler.ast;

import com.google.javascript.jscomp.parsing.parser.trees.*;
import com.sencha.tools.compiler.ast.js.BaseNode;

public interface ClosureASTConverter {

    //BaseNode convert (AmbientDeclarationTree tree);
    BaseNode convert (ArgumentListTree tree);
    BaseNode convert (ArrayLiteralExpressionTree tree);
    // BaseNode convert (ArrayPatternTree tree);
    //BaseNode convert (ArrayTypeTree tree);
    //BaseNode convert (AssignmentRestElementTree tree);
    BaseNode convert (AwaitExpressionTree tree);
    BaseNode convert (BinaryOperatorTree tree);
    BaseNode convert (BlockTree tree);
    BaseNode convert (BreakStatementTree tree);
    BaseNode convert (CallExpressionTree tree);
    //BaseNode convert (CallSignatureTree tree);
    BaseNode convert (CaseClauseTree tree);
    BaseNode convert (CatchTree tree);
    BaseNode convert (ClassDeclarationTree tree);
    BaseNode convert (CommaExpressionTree tree);
    BaseNode convert (Comment tree); // TODO should this be added into ClosureConverter.java? Probably not.
    BaseNode convert (ComprehensionForTree tree);
    BaseNode convert (ComprehensionIfTree tree);
    BaseNode convert (ComprehensionTree tree);
    BaseNode convert (ComputedPropertyDefinitionTree tree);
    BaseNode convert (ComputedPropertyGetterTree tree);
    //BaseNode convert (ComputedPropertyMemberVariableTree tree);
    BaseNode convert (ComputedPropertyMethodTree tree);
    BaseNode convert (ComputedPropertySetterTree tree);
    BaseNode convert (ConditionalExpressionTree tree);
    BaseNode convert (ContinueStatementTree tree);
    BaseNode convert (DebuggerStatementTree tree);
    BaseNode convert (DefaultClauseTree tree);
    BaseNode convert (DefaultParameterTree tree);
    BaseNode convert (DoWhileStatementTree tree);
    BaseNode convert (DynamicImportTree tree); // New
    BaseNode convert (EmptyStatementTree tree);
    //BaseNode convert (EnumDeclarationTree tree);
    BaseNode convert (ExportDeclarationTree tree);
    BaseNode convert (ExportSpecifierTree tree);
    BaseNode convert (ExpressionStatementTree tree);
    BaseNode convert (FieldDeclarationTree tree); // New
    BaseNode convert (FinallyTree tree);
    BaseNode convert (ForAwaitOfStatementTree tree); // New
    BaseNode convert (ForInStatementTree tree);
    BaseNode convert (FormalParameterListTree tree);
    BaseNode convert (ForOfStatementTree tree);
    BaseNode convert (ForStatementTree tree);
    BaseNode convert (FunctionDeclarationTree tree);
    //BaseNode convert (FunctionTypeTree tree);
    //BaseNode convert (GenericTypeListTree tree);
    BaseNode convert (GetAccessorTree tree);
    BaseNode convert (IdentifierExpressionTree tree);
    BaseNode convert (IfStatementTree tree);
    BaseNode convert (ImportDeclarationTree tree);
    BaseNode convert (ImportMetaExpressionTree tree); // New
    BaseNode convert (ImportSpecifierTree tree);
    BaseNode convert (IterRestTree tree); // New
    BaseNode convert (IterSpreadTree tree);// New
    //BaseNode convert (IndexSignatureTree tree);
    //BaseNode convert (InterfaceDeclarationTree tree);
    BaseNode convert (LabelledStatementTree tree);
    BaseNode convert (LiteralExpressionTree tree);
    BaseNode convert (MemberExpressionTree tree);
    BaseNode convert (MemberLookupExpressionTree tree);
    //BaseNode convert (MemberVariableTree tree);
    BaseNode convert (MissingPrimaryExpressionTree tree);
    //BaseNode convert (NamespaceDeclarationTree tree);
    //BaseNode convert (NamespaceNameTree tree);
    BaseNode convert (NewExpressionTree tree);
    BaseNode convert (NewTargetExpressionTree tree);
    BaseNode convert (NullTree tree);
    // BaseNode convert (ObjectLiteralExpressionTree tree);
    // BaseNode convert (ObjectPatternTree tree);
    BaseNode convert (ObjectRestTree tree); // New
    BaseNode convert (ObjectSpreadTree tree); // New
    // BaseNode convert (OptChainCallExpressionTree tree); // New
    // BaseNode convert (OptionalMemberExpressionTree tree); // New
    // BaseNode convert (OptionalMemberLookupExpressionTree tree); // New
    //BaseNode convert (OptionalParameterTree tree);
    //BaseNode convert (ParameterizedTypeTree tree);
    BaseNode convert (ParenExpressionTree tree);
    BaseNode convert (ParseTree tree); // New
    BaseNode convert (ProgramTree tree);
    BaseNode convert (PropertyNameAssignmentTree tree);
    //BaseNode convert (RecordTypeTree tree);
    //BaseNode convert (RestParameterTree tree);
    BaseNode convert (ReturnStatementTree tree);
    BaseNode convert (SetAccessorTree tree);
    //BaseNode convert (SpreadExpressionTree tree);
    BaseNode convert (SuperExpressionTree tree);
    BaseNode convert (SwitchStatementTree tree);
    BaseNode convert (TemplateLiteralExpressionTree tree);
    BaseNode convert (TemplateLiteralPortionTree tree);
    BaseNode convert (TemplateSubstitutionTree tree);
    BaseNode convert (ThisExpressionTree tree);
    BaseNode convert (ThrowStatementTree tree);
    BaseNode convert (TryStatementTree tree);
    //BaseNode convert (TypeAliasTree tree);
    //BaseNode convert (TypeNameTree tree);
    //BaseNode convert (TypeQueryTree tree);
    //BaseNode convert (TypedParameterTree tree);
    //BaseNode convert (UnaryExpressionTree tree);
    //BaseNode convert (UnionTypeTree tree);
    BaseNode convert (UnaryExpressionTree tree);
    BaseNode convert (UpdateExpressionTree tree);
    BaseNode convert (VariableDeclarationListTree tree);
    BaseNode convert (VariableDeclarationTree tree);
    BaseNode convert (VariableStatementTree tree);
    BaseNode convert (WhileStatementTree tree);
    BaseNode convert (WithStatementTree tree);
    BaseNode convert (YieldExpressionTree tree);

    BaseNode doConvert (ParseTree tree, BaseNode parent);
}
